import json
import os
import sys
import gc
import psutil
from pathlib import Path
from typing import List, Dict, Optional
import logging
from dataclasses import dataclass

# Libraries for PowerPoint
from openai import AzureOpenAI
from pptx import Presentation
from pptx.shapes.base import BaseShape
from pptx.text.text import TextFrame
from pptx.shapes.shapetree import SlideShapes
from pptx.enum.shapes import MSO_SHAPE_TYPE

# LangChain imports
from langchain.schema import BaseMessage, HumanMessage
from langchain.prompts import PromptTemplate
from langchain.chains.llm import LLMChain
from langchain.schema.runnable import RunnablePassthrough
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import AzureChatOpenAI as LangChainAzureOpenAI
from langchain_core.output_parsers import StrOutputParser


from src.agents.eproexcella_agent.models.testual import TextTranslator
from src.backend.blueprints.translator_bot.translator import Translator
from utils.core import get_logger
from config.config import EproExcelLaConfig
from utils.core import Singleton

# Logging configuration
FILE_DIR = os.path.dirname(__file__)
logger = get_logger(__file__)

class PPTXHandler:

    def __init__(self, pptx_path, file_context=None):
        self.pptx_path = pptx_path
        self.file_context = file_context
        self._memory_threshold_mb = 500  # Memory threshold for cleanup
        self._batch_size_adaptive = True  # Enable adaptive batch sizing
        self._presentation_size_factor = 1.0  # Scaling factor based on presentation size
        self._performance_history = []  # Track performance for adaptive optimization

    def _get_memory_usage_mb(self) -> float:
        """Get current memory usage in MB"""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0

    def _force_garbage_collection(self):
        """Force garbage collection and log memory usage"""
        memory_before = self._get_memory_usage_mb()
        gc.collect()
        memory_after = self._get_memory_usage_mb()
        logger.debug(f"Memory cleanup: {memory_before:.1f}MB -> {memory_after:.1f}MB (freed {memory_before - memory_after:.1f}MB)")

    def _get_adaptive_batch_size(self, default_size: int = 50, operation_type: str = "general") -> int:
        """Calculate adaptive batch size based on available memory, system load, and presentation characteristics"""
        if not self._batch_size_adaptive:
            return default_size

        try:
            memory_usage = self._get_memory_usage_mb()
            memory_info = psutil.virtual_memory()
            available_memory = memory_info.available / 1024 / 1024
            memory_percent = memory_info.percent

            # Base size adjustment based on memory pressure
            size_factor = 1.0

            # Aggressive reduction for high memory usage
            if memory_usage > self._memory_threshold_mb * 1.5:
                size_factor *= 0.25  # Very small batches
            elif memory_usage > self._memory_threshold_mb:
                size_factor *= 0.5   # Small batches
            elif memory_percent > 85:
                size_factor *= 0.6   # Reduced batches for high system memory usage
            elif available_memory < 500:  # Less than 500MB available
                size_factor *= 0.4
            elif available_memory < 1000:  # Less than 1GB available
                size_factor *= 0.7

            # Adjust based on presentation size factor
            size_factor *= self._presentation_size_factor

            # Operation-specific adjustments
            if operation_type == "table":
                size_factor *= 0.6  # Tables are more memory intensive
            elif operation_type == "notes":
                size_factor *= 0.8  # Notes processing is lighter

            # Apply historical performance adjustments
            if self._performance_history:
                avg_performance = sum(self._performance_history) / len(self._performance_history)
                if avg_performance > 2.0:  # Slow performance, reduce batch size
                    size_factor *= 0.8
                elif avg_performance < 0.5:  # Fast performance, can increase batch size
                    size_factor *= 1.2

            # Calculate final batch size
            adaptive_size = max(5, int(default_size * size_factor))

            logger.debug(f"Adaptive batch size: {adaptive_size} (default: {default_size}, factor: {size_factor:.2f}, memory: {memory_usage:.1f}MB, available: {available_memory:.1f}MB)")

            return adaptive_size

        except Exception as e:
            logger.warning(f"Error calculating adaptive batch size: {e}")
            return default_size

    def _update_presentation_size_factor(self, total_slides: int, avg_shapes_per_slide: float):
        """Update the presentation size factor based on presentation characteristics"""
        # Large presentations need smaller batches
        if total_slides > 100:
            self._presentation_size_factor = 0.5
        elif total_slides > 50:
            self._presentation_size_factor = 0.7
        elif total_slides > 20:
            self._presentation_size_factor = 0.85
        else:
            self._presentation_size_factor = 1.0

        # Adjust for shape complexity
        if avg_shapes_per_slide > 20:
            self._presentation_size_factor *= 0.7
        elif avg_shapes_per_slide > 10:
            self._presentation_size_factor *= 0.85

        logger.debug(f"Presentation size factor: {self._presentation_size_factor:.2f} (slides: {total_slides}, avg shapes: {avg_shapes_per_slide:.1f})")

    def _record_performance(self, operation_time: float):
        """Record performance metrics for adaptive optimization"""
        self._performance_history.append(operation_time)
        # Keep only recent history
        if len(self._performance_history) > 10:
            self._performance_history.pop(0)

    def _memory_monitor_context(self, operation_name: str):
        """Context manager for monitoring memory usage during operations"""
        return MemoryMonitorContext(operation_name, self)

class MemoryMonitorContext:
    """Context manager for monitoring memory usage during operations"""

    def __init__(self, operation_name: str, handler):
        self.operation_name = operation_name
        self.handler = handler
        self.start_memory = 0
        self.peak_memory = 0

    def __enter__(self):
        self.start_memory = self.handler._get_memory_usage_mb()
        self.peak_memory = self.start_memory
        logger.debug(f"Starting {self.operation_name} (Memory: {self.start_memory:.1f}MB)")
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        end_memory = self.handler._get_memory_usage_mb()
        self.peak_memory = max(self.peak_memory, end_memory)

        if exc_type is None:
            logger.debug(f"Completed {self.operation_name} (Memory: {self.start_memory:.1f}MB -> {end_memory:.1f}MB, Peak: {self.peak_memory:.1f}MB)")
        else:
            logger.error(f"Failed {self.operation_name} (Memory: {self.start_memory:.1f}MB -> {end_memory:.1f}MB, Peak: {self.peak_memory:.1f}MB)")

        # Force cleanup if memory usage increased significantly
        if end_memory > self.start_memory + 50:  # More than 50MB increase
            self.handler._force_garbage_collection()

    def update_peak(self):
        """Update peak memory usage"""
        current_memory = self.handler._get_memory_usage_mb()
        self.peak_memory = max(self.peak_memory, current_memory)

    def _process_shape_recursively(self, shape, target_lang: str, depth: int = 0) -> None:
        indent = "  " * depth
        logger.debug(f"{indent}Processing shape type: {shape.shape_type}")
        
        # Handle grouped shapes
        if shape.shape_type == MSO_SHAPE_TYPE.GROUP:
            logger.info(f"{indent}Found GROUP shape with {len(shape.shapes)} child shapes")
            for child_shape in shape.shapes:
                self._process_shape_recursively(child_shape, target_lang, depth + 1)

        # Handle table shapes
        elif shape.shape_type == MSO_SHAPE_TYPE.TABLE:
            logger.info(f"{indent}Found TABLE shape, translating table content")
            self._translate_table_content(shape.table, target_lang)

        # Handle shapes with text_frame
        elif shape.has_text_frame:
            logger.debug(f"{indent}Found text shape, translating content")
            self._translate_text_frame_runs_preserve_layout(shape.text_frame, target_lang)

        # Fallback: shapes with `.text` but no `.text_frame`
        elif hasattr(shape, "text") and shape.text.strip():
            logger.debug(f"{indent}Fallback: shape has .text but no .text_frame — translating")
            try:
                translated_text = self.batch_translate([shape.text], target_lang)[0]
                shape.text = translated_text
            except Exception as e:
                logger.warning(f"{indent}Could not update shape.text: {e}")

        else:
            logger.debug(f"{indent}Shape type {shape.shape_type} - no translatable content")

    def _translate_table_content(self, table, target_lang: str) -> None:
        logger.info(f"Translating table with {len(table.rows)} rows and {len(table.columns)} columns")

        # Use streaming approach to avoid accumulating all text in memory
        self._translate_table_content_streaming(table, target_lang)

    def _translate_table_content_streaming(self, table, target_lang: str) -> None:
        """
        Stream-based table translation that processes cells in batches
        to reduce memory usage for large tables.
        """
        batch_size = self._get_adaptive_batch_size(30, "table")  # Table-specific batch sizing
        current_batch_runs = []
        current_batch_texts = []

        with self._memory_monitor_context(f"table translation ({len(table.rows)}x{len(table.columns)})") as monitor:
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    if cell.text and cell.text.strip():
                        for paragraph in cell.text_frame.paragraphs:
                            for run in paragraph.runs:
                                if run.text.strip():  # Only process non-empty runs
                                    current_batch_runs.append(run)
                                    current_batch_texts.append(run.text)

                                    # Process batch when it reaches the target size
                                    if len(current_batch_texts) >= batch_size:
                                        self._process_text_batch(current_batch_runs, current_batch_texts, target_lang, "table")
                                        current_batch_runs.clear()
                                        current_batch_texts.clear()
                                        monitor.update_peak()

                                        # Recalculate batch size periodically for large tables
                                        if row_idx % 10 == 0:
                                            new_batch_size = self._get_adaptive_batch_size(30, "table")
                                            if new_batch_size != batch_size:
                                                batch_size = new_batch_size
                                                logger.debug(f"Adjusted table batch size to {batch_size}")

            # Process any remaining texts in the final batch
            if current_batch_texts:
                self._process_text_batch(current_batch_runs, current_batch_texts, target_lang, "table")
                current_batch_runs.clear()
                current_batch_texts.clear()

    def _process_text_batch(self, runs: List, texts: List[str], target_lang: str, operation_type: str = "general") -> None:
        """Process a batch of text runs for translation"""
        if not texts:
            return

        try:
            translated_texts = self.batch_translate(texts, target_lang, operation_type=operation_type)
            for run, translated_text in zip(runs, translated_texts):
                run.text = translated_text
        except Exception as e:
            logger.error(f"Error processing text batch: {e}")
            # Keep original text on error

    def _translate_slide(self, slide, target_lang: str) -> None:
        with self._memory_monitor_context(f"slide translation ({len(slide.shapes)} shapes)") as monitor:
            shape_counts = {}
            for shape in slide.shapes:
                shape_type = shape.shape_type
                shape_counts[shape_type] = shape_counts.get(shape_type, 0) + 1
            if MSO_SHAPE_TYPE.GROUP in shape_counts:
                logger.info(f"Slide contains {shape_counts[MSO_SHAPE_TYPE.GROUP]} grouped element(s)")
            if MSO_SHAPE_TYPE.TABLE in shape_counts:
                logger.info(f"Slide contains {shape_counts[MSO_SHAPE_TYPE.TABLE]} table(s)")

            # Process shapes with memory monitoring
            for i, shape in enumerate(slide.shapes):
                self._process_shape_recursively(shape, target_lang)

                # Update peak memory tracking
                if i % 3 == 0:
                    monitor.update_peak()

                # Force cleanup every few shapes if memory usage is high
                if i % 5 == 0 and self._get_memory_usage_mb() > self._memory_threshold_mb:
                    self._force_garbage_collection()

    def batch_translate(self, texts: List[str], target_lang: str, batch_size: int = 50, operation_type: str = "general") -> List[str]:
        import time
        start_time = time.time()

        # Use enhanced adaptive batch sizing
        adaptive_batch_size = self._get_adaptive_batch_size(batch_size, operation_type)
        logger.debug(f"Using batch size: {adaptive_batch_size} (requested: {batch_size}, operation: {operation_type})")

        all_translations = []
        translator = Translator()
        prompt = translator.get_default_prompt(target_lang)

        with self._memory_monitor_context(f"batch translation ({len(texts)} texts)") as monitor:
            for i in range(0, len(texts), adaptive_batch_size):
                batch_start_time = time.time()
                batch = texts[i:i+adaptive_batch_size]
                non_empty_batch = []
                non_empty_indices = []

                for j, text in enumerate(batch):
                    if text.strip():
                        non_empty_batch.append(text)
                        non_empty_indices.append(j)

                if not non_empty_batch:
                    all_translations.extend(batch)
                    continue

                dict_data = {str(k+1): text for k, text in enumerate(non_empty_batch)}

                try:
                    result = translator.submit_to_gpt(dict_data, prompt) if self.file_context is None else translator.submit_to_gpt(dict_data, prompt, self.file_context)
                    if result == '{}':
                        translated_batch = batch[:]
                    else:
                        translated_texts = json.loads(result)
                        translated_batch = batch[:]
                        for k, original_idx in enumerate(non_empty_indices):
                            key = str(k+1)
                            if key in translated_texts:
                                translated_batch[original_idx] = translated_texts[key]
                    all_translations.extend(translated_batch)

                    # Clear intermediate variables to free memory
                    del dict_data, translated_batch
                    if 'translated_texts' in locals():
                        del translated_texts

                except Exception as e:
                    logger.error(f"Translation batch error: {e}")
                    all_translations.extend(batch)

                # Record batch performance
                batch_time = time.time() - batch_start_time
                self._record_performance(batch_time)

                # Update memory monitoring
                if i % 3 == 0:
                    monitor.update_peak()

                # Dynamic batch size adjustment based on performance
                if i > 0 and i % (adaptive_batch_size * 2) == 0:
                    # Recalculate batch size based on current conditions
                    new_batch_size = self._get_adaptive_batch_size(batch_size, operation_type)
                    if new_batch_size != adaptive_batch_size:
                        logger.debug(f"Adjusting batch size mid-operation: {adaptive_batch_size} -> {new_batch_size}")
                        adaptive_batch_size = new_batch_size

                # Force garbage collection every few batches if memory usage is high
                if i % (adaptive_batch_size * 3) == 0 and self._get_memory_usage_mb() > self._memory_threshold_mb:
                    self._force_garbage_collection()

        total_time = time.time() - start_time
        logger.info(f"Batch translation completed in {total_time:.2f}s for {len(texts)} texts")
        return all_translations

    def _translate_text_frame_runs_preserve_layout(self, text_frame, target_lang):
        """
        Optimized text frame translation that processes paragraphs individually
        to reduce memory usage and improve layout preservation.
        """
        for paragraph_idx, paragraph in enumerate(text_frame.paragraphs):
            runs = paragraph.runs
            if not runs:
                continue

            # Process runs in smaller groups to reduce memory usage
            self._translate_paragraph_runs(runs, target_lang)

            # Periodic cleanup for large text frames
            if paragraph_idx % 10 == 0 and paragraph_idx > 0:
                self._force_garbage_collection()

    def _translate_paragraph_runs(self, runs, target_lang):
        """Translate runs within a single paragraph while preserving layout"""
        original_texts = [run.text for run in runs]
        combined_text = " ".join(original_texts).strip()

        if not combined_text:
            return

        try:
            # Translate the combined text
            translated_text = self.batch_translate([combined_text], target_lang)[0]

            # Distribute translated text proportionally across runs
            total_original_chars = sum(len(t) for t in original_texts)
            if total_original_chars == 0:
                return

            translated_fragments = []
            start_idx = 0

            for original_text in original_texts:
                proportion = len(original_text) / total_original_chars
                chars_to_take = round(proportion * len(translated_text))
                translated_fragments.append(translated_text[start_idx:start_idx + chars_to_take])
                start_idx += chars_to_take

            # Handle any remaining characters
            if start_idx < len(translated_text):
                translated_fragments[-1] += translated_text[start_idx:]

            # Apply translated fragments to runs
            for run, new_text in zip(runs, translated_fragments):
                run.text = new_text

            # Clear intermediate variables
            del original_texts, translated_fragments, translated_text

        except Exception as e:
            logger.error(f"Error translating paragraph runs: {e}")
            # Keep original text on error

    def get_slide_statistics(self, slide) -> Dict[str, int]:
        stats = {
            'total_shapes': 0,
            'grouped_shapes': 0,
            'tables': 0,
            'text_shapes': 0,
            'nested_depth': 0
        }
        def count_shapes_recursive(shapes, depth=0):
            stats['nested_depth'] = max(stats['nested_depth'], depth)
            for shape in shapes:
                stats['total_shapes'] += 1
                if shape.shape_type == MSO_SHAPE_TYPE.GROUP:
                    stats['grouped_shapes'] += 1
                    count_shapes_recursive(shape.shapes, depth + 1)
                elif shape.shape_type == MSO_SHAPE_TYPE.TABLE:
                    stats['tables'] += 1
                elif shape.has_text_frame and shape.text.strip():
                    stats['text_shapes'] += 1
        count_shapes_recursive(slide.shapes)
        return stats

    def translate_presentation(self, target_language: str) -> str:
        try:
            initial_memory = self._get_memory_usage_mb()
            logger.info(f"Loading presentation: {self.pptx_path} (Initial memory: {initial_memory:.1f}MB)")

            presentation = Presentation(self.pptx_path)
            total_slides = len(presentation.slides)

            # Calculate presentation characteristics for adaptive optimization
            total_shapes = 0
            for slide in presentation.slides:
                slide_stats = self.get_slide_statistics(slide)
                total_shapes += slide_stats['total_shapes']

            avg_shapes_per_slide = total_shapes / total_slides if total_slides > 0 else 0
            self._update_presentation_size_factor(total_slides, avg_shapes_per_slide)

            load_memory = self._get_memory_usage_mb()
            logger.info(f"Starting translation of {total_slides} slides, {total_shapes} total shapes (Memory after load: {load_memory:.1f}MB)")

            for slide_num, slide in enumerate(presentation.slides, 1):
                logger.info(f"Translating slide {slide_num}/{total_slides}")
                slide_stats = self.get_slide_statistics(slide)
                logger.debug(f"Slide {slide_num} statistics: {slide_stats}")

                # Translate slide content
                self._translate_slide(slide, target_language)

                # Process notes slide if present
                if hasattr(slide, 'notes_slide') and slide.notes_slide:
                    logger.info(f"Processing notes for slide {slide_num}")
                    self._translate_notes_slide(slide.notes_slide, target_language)

                # Adaptive cleanup frequency based on presentation size
                cleanup_frequency = max(3, min(10, total_slides // 10))
                if slide_num % cleanup_frequency == 0:
                    self._force_garbage_collection()
                    current_memory = self._get_memory_usage_mb()
                    logger.info(f"Processed {slide_num}/{total_slides} slides (Memory: {current_memory:.1f}MB)")

            final_memory = self._get_memory_usage_mb()
            logger.info(f"Translation complete. Memory usage: {initial_memory:.1f}MB -> {final_memory:.1f}MB")
            return presentation

        except Exception as e:
            logger.error(f"Error during translation: {e}")
            # Force cleanup on error
            self._force_garbage_collection()
            return None

    def _translate_notes_slide(self, notes_slide, target_language: str) -> None:
        """Translate notes slide content with memory optimization"""
        notes_runs = []
        notes_texts = []

        for shape in notes_slide.shapes:
            if shape.has_text_frame:
                for paragraph in shape.text_frame.paragraphs:
                    for run in paragraph.runs:
                        if run.text.strip():  # Only process non-empty text
                            notes_runs.append(run)
                            notes_texts.append(run.text)

        if notes_texts:
            with self._memory_monitor_context(f"notes translation ({len(notes_texts)} texts)") as monitor:
                # Process notes in smaller batches to reduce memory usage
                batch_size = self._get_adaptive_batch_size(20, "notes")  # Notes-specific batch sizing
                for i in range(0, len(notes_texts), batch_size):
                    batch_texts = notes_texts[i:i+batch_size]
                    batch_runs = notes_runs[i:i+batch_size]

                    translated_batch = self.batch_translate(batch_texts, target_language, operation_type="notes")
                    for run, translated_text in zip(batch_runs, translated_batch):
                        run.text = translated_text

                    # Update memory monitoring
                    monitor.update_peak()

                    # Clear batch variables
                    del batch_texts, batch_runs, translated_batch

    def translate_presentation_multi_language(self, target_languages: List[str]) -> Dict[str, any]:
        """
        Optimized multi-language translation that loads the presentation once
        and creates copies for each language to reduce memory usage and I/O overhead.
        """
        results = {}
        original_presentation = None

        try:
            initial_memory = self._get_memory_usage_mb()
            logger.info(f"Starting multi-language translation for {len(target_languages)} languages (Memory: {initial_memory:.1f}MB)")

            # Load the original presentation once
            logger.info(f"Loading original presentation: {self.pptx_path}")
            original_presentation = Presentation(self.pptx_path)
            total_slides = len(original_presentation.slides)

            load_memory = self._get_memory_usage_mb()
            logger.info(f"Original presentation loaded with {total_slides} slides (Memory: {load_memory:.1f}MB)")

            for lang_index, target_language in enumerate(target_languages):
                logger.info(f"Processing language {lang_index + 1}/{len(target_languages)}: {target_language}")

                try:
                    # Create a copy of the presentation for this language
                    lang_presentation = self._create_presentation_copy(original_presentation)

                    # Translate the copy
                    translated_presentation = self._translate_presentation_in_place(lang_presentation, target_language)

                    if translated_presentation:
                        results[target_language] = translated_presentation
                        logger.info(f"Successfully translated presentation for {target_language}")
                    else:
                        logger.error(f"Failed to translate presentation for {target_language}")
                        results[target_language] = None

                    # Force cleanup after each language
                    self._force_garbage_collection()
                    current_memory = self._get_memory_usage_mb()
                    logger.info(f"Completed {target_language} (Memory: {current_memory:.1f}MB)")

                except Exception as e:
                    logger.error(f"Error translating to {target_language}: {e}")
                    results[target_language] = None

            final_memory = self._get_memory_usage_mb()
            logger.info(f"Multi-language translation complete. Memory: {initial_memory:.1f}MB -> {final_memory:.1f}MB")

            return results

        except Exception as e:
            logger.error(f"Error in multi-language translation: {e}")
            return {}
        finally:
            # Cleanup original presentation
            if original_presentation:
                del original_presentation
            self._force_garbage_collection()

    def _create_presentation_copy(self, original_presentation):
        """
        Create a copy of the presentation by saving to a temporary file and reloading.
        This is more memory efficient than deep copying the object structure.
        """
        import tempfile

        try:
            # Create a temporary file for the copy
            with tempfile.NamedTemporaryFile(suffix='.pptx', delete=False) as temp_file:
                temp_path = temp_file.name

            # Save the original to the temporary file
            original_presentation.save(temp_path)

            # Load the copy from the temporary file
            copy_presentation = Presentation(temp_path)

            # Clean up the temporary file
            try:
                os.unlink(temp_path)
            except Exception as e:
                logger.warning(f"Could not delete temporary file {temp_path}: {e}")

            return copy_presentation

        except Exception as e:
            logger.error(f"Error creating presentation copy: {e}")
            return None

    def _translate_presentation_in_place(self, presentation, target_language: str):
        """
        Translate a presentation object in place without reloading from file.
        """
        try:
            total_slides = len(presentation.slides)
            logger.info(f"Translating {total_slides} slides for {target_language}")

            for slide_num, slide in enumerate(presentation.slides, 1):
                logger.debug(f"Translating slide {slide_num}/{total_slides} for {target_language}")

                # Translate slide content
                self._translate_slide(slide, target_language)

                # Process notes slide if present
                if hasattr(slide, 'notes_slide') and slide.notes_slide:
                    self._translate_notes_slide(slide.notes_slide, target_language)

                # Periodic cleanup during translation
                if slide_num % 10 == 0:
                    self._force_garbage_collection()

            return presentation

        except Exception as e:
            logger.error(f"Error in in-place translation for {target_language}: {e}")
            return None

    def write_result_to_file(self, presentation: Presentation, lang: str):
        logger.info(f"Writing results to PowerPoint for language: {lang}")
        original_base, original_ext = os.path.splitext(self.pptx_path)
        translated_path = f"{original_base}_{lang}{original_ext}"
        
        # Ensure the directory exists
        directory = os.path.dirname(translated_path)
        if not os.path.exists(directory):
            logger.info(f"Creating directory: {directory}")
            os.makedirs(directory, exist_ok=True)
        
        try:
            presentation.save(translated_path)
            logger.info(f"Successfully saved translated PowerPoint file: {translated_path}")
        except Exception as e:
            logger.error(f"Error saving translated PowerPoint file: {e}")
            raise